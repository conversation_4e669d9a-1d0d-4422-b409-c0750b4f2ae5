import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import mongoose from "mongoose";
import { z } from "zod";

// Schema validation for categories
const CategorySchema = z.object({
  name: z.string().min(1, "Category name is required"),
  description: z.string().optional(),
});

// Define a simple Category model if it doesn't exist
let Category: mongoose.Model<any>;

try {
  // Try to get the existing model
  Category = mongoose.model('Category');
} catch (error) {
  // Define the model if it doesn't exist
  const categorySchema = new mongoose.Schema({
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    slug: {
      type: String,
      unique: true,
      trim: true,
      lowercase: true,
    },
    count: {
      type: Number,
      default: 0,
    },
  }, { timestamps: true });

  // Create slug from name before saving
  categorySchema.pre('save', function(next) {
    if (this.isModified('name')) {
      this.slug = this.name.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]+/g, '');
    }
    next();
  });

  Category = mongoose.model('Category', categorySchema);
}

// GET all categories
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    // Import BlogPost model for counting
    const BlogPost = (await import("@/models/BlogPost")).default;

    const categories = await Category.find().sort({ name: 1 });

    // Calculate post counts for each category
    const categoriesWithCounts = await Promise.all(
      categories.map(async (category) => {
        const count = await BlogPost.countDocuments({
          categoryId: category._id,
          status: "published",
          visibility: "public"
        });

        return {
          ...category.toObject(),
          count
        };
      })
    );

    return NextResponse.json({
      success: true,
      data: categoriesWithCounts,
      count: categoriesWithCounts.length
    });
  } catch (error: any) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch categories",
        data: [],
        count: 0
      },
      { status: 500 }
    );
  }
}

// POST a new category
export async function POST(request: NextRequest) {
  try {
    // Check authentication using request headers (set by middleware)
    const userRole = request.headers.get("x-user-role");

    // Only admin users can create categories
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    await connectToDatabase();

    const body = await request.json();
    const validation = CategorySchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validation.error },
        { status: 400 }
      );
    }

    // Check if category already exists (case-insensitive)
    const existingCategory = await Category.findOne({
      name: { $regex: new RegExp(`^${validation.data.name.trim()}$`, 'i') }
    });

    if (existingCategory) {
      return NextResponse.json(
        {
          error: "Category already exists",
          existingCategory: {
            _id: existingCategory._id,
            name: existingCategory.name,
            slug: existingCategory.slug
          }
        },
        { status: 409 }
      );
    }

    // Create new category
    const newCategory = await Category.create(validation.data);

    return NextResponse.json(newCategory, { status: 201 });
  } catch (error: any) {
    console.error("Error creating category:", error);
    return NextResponse.json(
      { error: "Failed to create category" },
      { status: 500 }
    );
  }
}
