'use client';

import Link from "next/link";
import { motion } from "framer-motion";
import { Search, BookOpen, Tag as TagIcon, Calendar, User, ArrowRight, ChevronLeft, ChevronRight, ArrowLeft } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { useTheme } from "@/hooks/useTheme";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/layout/Header";

interface BlogPost {
  id: string;
  _id: string;
  title: string;
  excerpt: string;
  content: string;
  slug: string;
  featuredImage: string;
  imageCredit: string;
  categories: string[];
  tags: string[];
  publishedAt: string;
  author: { name: string; email: string };
  categoryId?: string;
}

interface Category {
  _id: string;
  name: string;
  count: number;
}

export default function AllBlogsPage() {
  const { theme } = useTheme();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [totalPosts, setTotalPosts] = useState(0);
  const resultsRef = useRef<HTMLDivElement>(null);

  const postsPerPage = 15; // 15 posts per page as requested

  // Live search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setSearchQuery(searchTerm);
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories');
        const data = await response.json();
        if (data.success) {
          setCategories(data.data);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);

  // Fetch posts when search, category, or page changes
  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: currentPage.toString(),
          limit: postsPerPage.toString(),
          status: 'published'
        });

        if (searchQuery.trim()) {
          params.append('search', searchQuery);
        }

        if (selectedCategory) {
          params.append('category', selectedCategory);
        }

        const response = await fetch(`/api/posts?${params}`);
        const data = await response.json();

        if (data.success) {
          setPosts(data.data);
          setTotalPages(data.pagination.totalPages);
          setTotalPosts(data.pagination.total);
        } else {
          setPosts([]);
          setTotalPages(0);
          setTotalPosts(0);
        }
      } catch (error) {
        console.error('Error fetching posts:', error);
        setPosts([]);
        setTotalPages(0);
        setTotalPosts(0);
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, [searchQuery, selectedCategory, currentPage]);

  // Handle category selection
  const handleCategorySelect = (category: string | null) => {
    setSelectedCategory(category);
    setCurrentPage(1); // Reset to first page when category changes
    // Scroll to results when category is selected
    setTimeout(() => {
      resultsRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }, 100);
  };

  // Handle back to blog
  const handleBackToBlog = () => {
    router.push('/blog');
  };

  const isDark = theme === 'dark';

  return (
    <div className={`min-h-screen ${isDark
      ? 'bg-gradient-to-br from-gray-900 via-black to-gray-900'
      : 'bg-gradient-to-br from-blue-50 via-white to-purple-50'
    }`}>
      {/* Header */}
      <Header />

      {/* Hero Section */}
      <motion.section
        className="py-16 px-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="max-w-7xl mx-auto">
          {/* Back Button */}
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1, duration: 0.5 }}
          >
            <Button
              onClick={handleBackToBlog}
              variant="outline"
              className={`flex items-center gap-2 rounded-full px-6 py-3 transition-all duration-300 hover:scale-105 ${
                isDark
                  ? 'border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-purple-400'
                  : 'border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-blue-600'
              }`}
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Blog Home
            </Button>
          </motion.div>

          <div className="text-center">
            <motion.h1
              className={`text-4xl md:text-5xl lg:text-6xl font-bold mb-6 ${
                isDark
                  ? 'bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent'
                  : 'bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent'
              }`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              📝 All Blog Articles
            </motion.h1>

            <motion.p
              className={`text-xl md:text-2xl mb-8 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              Comprehensive insights, guides, and tips from our experts
            </motion.p>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-wrap justify-center gap-4 mb-12"
            >
              <div className={`flex items-center px-6 py-3 rounded-full ${
                isDark
                  ? 'bg-purple-900/30 text-purple-300 border border-purple-700/50'
                  : 'bg-purple-100 text-purple-700 border border-purple-200'
              } backdrop-blur-sm`}>
                <BookOpen className="mr-2 h-5 w-5" /> {totalPosts} Articles
              </div>
              <div className={`flex items-center px-6 py-3 rounded-full ${
                isDark
                  ? 'bg-blue-900/30 text-blue-300 border border-blue-700/50'
                  : 'bg-blue-100 text-blue-700 border border-blue-200'
              } backdrop-blur-sm`}>
                <TagIcon className="mr-2 h-5 w-5" /> {categories.length} Categories
              </div>
            </motion.div>

            {/* Enhanced Search Bar */}
            <motion.div
              className="max-w-3xl mx-auto mb-8"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              <div className="relative group">
                <div className={`absolute inset-0 rounded-full blur-md opacity-75 transition-all duration-300 ${
                  isDark 
                    ? 'bg-purple-500/10 group-hover:bg-purple-500/20' 
                    : 'bg-blue-500/10 group-hover:bg-blue-500/20'
                }`}></div>
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search articles by title, content, or keywords..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className={`pl-12 pr-4 py-5 w-full rounded-full border-2 transition-all duration-300 ${
                      isDark
                        ? 'bg-gray-800/70 border-gray-700 text-white placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/30'
                        : 'bg-white/90 border-gray-200 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/30'
                    } backdrop-blur-sm text-lg shadow-lg`}
                  />
                  {searchTerm && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2"
                    >
                      <div className={`w-2 h-2 rounded-full animate-pulse ${
                        isDark ? 'bg-purple-400' : 'bg-blue-500'
                      }`} />
                    </motion.div>
                  )}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Category Filter Section */}
      <section className="py-8 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex flex-wrap justify-center gap-3"
          >
            <motion.button
              whileHover={{ y: -2, scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`px-6 py-3 rounded-full transition-all duration-300 font-medium ${
                selectedCategory === null
                  ? (isDark
                      ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/25'
                      : 'bg-blue-600 text-white shadow-lg shadow-blue-600/25')
                  : (isDark
                      ? 'bg-gray-800/50 border border-gray-700 text-gray-300 hover:bg-gray-700/50 backdrop-blur-sm'
                      : 'bg-white/80 border border-gray-200 text-gray-700 hover:bg-gray-50 backdrop-blur-sm shadow-sm')
              }`}
              onClick={() => handleCategorySelect(null)}
            >
              All Categories
            </motion.button>

            {categories.map((category, index) => (
              <motion.button
                key={category._id}
                whileHover={{ y: -2, scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 + (index * 0.05) }}
                className={`px-6 py-3 rounded-full transition-all duration-300 font-medium ${
                  selectedCategory === category._id
                    ? (isDark
                        ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/25'
                        : 'bg-blue-600 text-white shadow-lg shadow-blue-600/25')
                    : (isDark
                        ? 'bg-gray-800/50 border border-gray-700 text-gray-300 hover:bg-gray-700/50 backdrop-blur-sm'
                        : 'bg-white/80 border border-gray-200 text-gray-700 hover:bg-gray-50 backdrop-blur-sm shadow-sm')
                }`}
                onClick={() => handleCategorySelect(category._id)}
              >
                {category.name} ({category.count || 0})
              </motion.button>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Main Content Section */}
      <section ref={resultsRef} id="blog-results" className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 gap-8">
            {/* Blog Posts - Main Content */}
            <div>
              <motion.h2
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className={`text-3xl md:text-4xl font-bold mb-6 ${
                  isDark ? 'text-white' : 'text-gray-900'
                }`}
              >
                {searchQuery.trim() !== ''
                  ? `Search Results for "${searchQuery}"`
                  : selectedCategory
                    ? `Articles in ${categories.find(cat => cat._id === selectedCategory)?.name || selectedCategory}`
                    : "All Articles"}
              </motion.h2>

              {/* Search results info */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="mb-8"
              >
                {searchQuery.trim() !== '' ? (
                  posts.length === 0 ? (
                    <p className={`text-lg ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                      No articles found matching your search.
                    </p>
                  ) : (
                    <p className={`text-lg ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                      Found <span className={`font-semibold ${isDark ? 'text-purple-400' : 'text-purple-600'}`}>
                        {totalPosts}
                      </span> article{totalPosts !== 1 ? 's' : ''}
                      {currentPage > 1 && ` (Page ${currentPage} of ${totalPages})`}
                    </p>
                  )
                ) : (
                  selectedCategory ? (
                    <p className={`text-lg ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                      Showing <span className={`font-semibold ${isDark ? 'text-purple-400' : 'text-purple-600'}`}>
                        {totalPosts}
                      </span> article{totalPosts !== 1 ? 's' : ''} in {categories.find(cat => cat._id === selectedCategory)?.name || selectedCategory}
                      {currentPage > 1 && ` (Page ${currentPage} of ${totalPages})`}
                    </p>
                  ) : (
                    <p className={`text-lg ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                      Showing <span className={`font-semibold ${isDark ? 'text-purple-400' : 'text-purple-600'}`}>
                        {((currentPage - 1) * postsPerPage) + 1}-{Math.min(currentPage * postsPerPage, totalPosts)}
                      </span> of <span className={`font-semibold ${isDark ? 'text-purple-400' : 'text-purple-600'}`}>
                        {totalPosts}
                      </span> articles
                      {currentPage > 1 && ` (Page ${currentPage} of ${totalPages})`}
                    </p>
                  )
                )}
              </motion.div>

              {/* Blog posts grid - 3 posts per row */}
              {loading ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="text-center py-12"
                >
                  <motion.div
                    className="inline-block h-12 w-12 rounded-full border-4 border-primary/20 border-t-primary"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  />
                  <p className={`mt-6 text-lg ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                    Loading articles...
                  </p>
                </motion.div>
              ) : posts.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  className="text-center py-12"
                >
                  <div className={`mx-auto w-24 h-24 rounded-full flex items-center justify-center mb-6 ${
                    isDark ? 'bg-gray-800 text-gray-400' : 'bg-gray-100 text-gray-500'
                  }`}>
                    <Search className="w-10 h-10" />
                  </div>
                  <h3 className={`text-xl font-medium mb-2 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                    No articles found
                  </h3>
                  <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                    Try adjusting your search or category filter to find what you're looking for.
                  </p>
                </motion.div>
              ) : (
                <>
                  <motion.div
                    initial="hidden"
                    animate="visible"
                    variants={{
                      hidden: { opacity: 0 },
                      visible: {
                        opacity: 1,
                        transition: {
                          staggerChildren: 0.05,
                          delayChildren: 0.1
                        }
                      }
                    }}
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
                  >
                    {posts.map((post, index) => (
                      <BlogCard
                        key={post.id}
                        post={post}
                        index={index}
                        isDark={isDark}
                        categories={categories}
                      />
                    ))}
                  </motion.div>

                  {/* Enhanced Pagination */}
                  {totalPages > 1 && (
                    <motion.div
                      className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-8"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4, duration: 0.5 }}
                    >
                      <div className="text-sm">
                        <span className={`${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                          Page {currentPage} of {totalPages} | Showing {((currentPage - 1) * postsPerPage) + 1}-{Math.min(currentPage * postsPerPage, totalPosts)} of {totalPosts} articles
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                          disabled={currentPage === 1}
                          className={`flex items-center gap-2 ${
                            isDark ? 'border-gray-700 text-gray-300 hover:bg-gray-800' : 'border-gray-300'
                          }`}
                        >
                          <ChevronLeft className="h-4 w-4" />
                          Previous
                        </Button>

                        <div className="flex gap-1">
                          {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                            let page;
                            if (totalPages <= 5) {
                              page = i + 1;
                            } else if (currentPage <= 3) {
                              page = i + 1;
                            } else if (currentPage >= totalPages - 2) {
                              page = totalPages - 4 + i;
                            } else {
                              page = currentPage - 2 + i;
                            }

                            return (
                              <Button
                                key={page}
                                variant={currentPage === page ? "default" : "outline"}
                                onClick={() => setCurrentPage(page)}
                                className={`min-w-10 h-10 ${
                                  currentPage === page
                                    ? (isDark ? 'bg-purple-600 text-white' : 'bg-blue-600 text-white')
                                    : (isDark ? 'border-gray-700 text-gray-300 hover:bg-gray-800' : 'border-gray-300')
                                }`}
                              >
                                {page}
                              </Button>
                            );
                          })}
                        </div>

                        <Button
                          variant="outline"
                          onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                          disabled={currentPage === totalPages}
                          className={`flex items-center gap-2 ${
                            isDark ? 'border-gray-700 text-gray-300 hover:bg-gray-800' : 'border-gray-300'
                          }`}
                        >
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>

      </section>

    </div>
  );
}

// Enhanced Blog Card Component
const BlogCard = ({ post, index, isDark, categories }: {
  post: BlogPost,
  index: number,
  isDark: boolean,
  categories: Category[]
}) => {
  // Calculate staggered delay based on index
  const delay = 0.05 * (index % 9); // Reset delay every 9 items for better UX

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  // Get category name
  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat._id === categoryId);
    return category?.name || categoryId;
  };

  return (
    <motion.article
      variants={{
        hidden: { opacity: 0, y: 30 },
        visible: {
          opacity: 1,
          y: 0,
          transition: {
            duration: 0.5,
            delay,
            ease: [0.25, 0.1, 0.25, 1.0]
          }
        }
      }}
      whileHover={{
        y: -10,
        scale: 1.02,
        transition: { duration: 0.3 }
      }}
      className={`rounded-2xl overflow-hidden transition-all duration-300 ${
        isDark
          ? 'bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-gray-700/50 backdrop-blur-sm shadow-lg hover:shadow-2xl'
          : 'bg-white/80 border border-gray-200/50 backdrop-blur-sm shadow-lg hover:shadow-2xl'
      }`}
    >
      <Link href={`/blog/${post.slug}`} className="block">
        <div className="relative overflow-hidden h-52">
          <motion.img
            src={post.featuredImage && post.featuredImage.trim() !== ''
              ? post.featuredImage
              : 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&q=80'
            }
            alt={post.title}
            className="w-full h-full object-cover"
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.6 }}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&q=80';
            }}
          />
          {post.categoryId && (
            <div className="absolute top-4 left-4">
              <Badge className={`${
                isDark
                  ? 'bg-purple-600/90 text-white'
                  : 'bg-blue-600/90 text-white'
              } backdrop-blur-sm`}>
                {getCategoryName(post.categoryId)}
              </Badge>
            </div>
          )}
          <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-black/70 to-transparent" />
        </div>
      </Link>

      <div className="p-6">
        <div className={`flex items-center text-xs gap-4 mb-3 ${
          isDark ? 'text-gray-400' : 'text-gray-500'
        }`}>
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>{formatDate(post.publishedAt)}</span>
          </div>
          <div className="flex items-center gap-1">
            <User className="h-3 w-3" />
            <span>{post.author.name}</span>
          </div>
        </div>

        <Link href={`/blog/${post.slug}`}>
          <h3 className={`text-xl font-bold mb-3 transition-colors ${
            isDark
              ? 'text-white hover:text-purple-400'
              : 'text-gray-900 hover:text-blue-600'
          }`}>
            {post.title}
          </h3>
        </Link>

        <p className={`mb-4 line-clamp-2 ${
          isDark ? 'text-gray-300' : 'text-gray-600'
        }`}>
          {post.excerpt}
        </p>

        <motion.div
          whileHover={{ x: 5 }}
          transition={{ duration: 0.2 }}
          className="flex items-center"
        >
          <Link
            href={`/blog/${post.slug}`}
            className={`font-medium inline-flex items-center transition-colors ${
              isDark
                ? 'text-purple-400 hover:text-purple-300'
                : 'text-blue-600 hover:text-blue-700'
            }`}
          >
            Read Article <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </motion.div>
      </div>
    </motion.article>
  );
};