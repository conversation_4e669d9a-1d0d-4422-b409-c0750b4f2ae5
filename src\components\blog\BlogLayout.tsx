"use client";

import { ReactNode, useRef, useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { BlogSidebar } from "./BlogSidebar";
import { MobileSidebar } from "./MobileSidebar";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";

interface BlogLayoutProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  showSidebar?: boolean;
}

export function BlogLayout({
  children,
  title,
  subtitle,
  showSidebar = true
}: BlogLayoutProps) {
  const mainRef = useRef<HTMLDivElement>(null);
  const [showScrollTop, setShowScrollTop] = useState(false);
  
  // Handle scroll events to show/hide the scroll-to-top button
  const handleScroll = () => {
    if (!mainRef.current) return;
    
    const scrollTop = mainRef.current.scrollTop;
    setShowScrollTop(scrollTop > 300);
  };
  
  // Scroll to top function
  const scrollToTop = () => {
    if (!mainRef.current) return;
    
    mainRef.current.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  
  // Add scroll event listener
  useEffect(() => {
    const mainElement = mainRef.current;
    if (mainElement) {
      mainElement.addEventListener('scroll', handleScroll);
      return () => mainElement.removeEventListener('scroll', handleScroll);
    }
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 }
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground transition-all duration-300 ease-in-out">
      <Header />

      <div className="flex flex-1 relative">
        {/* Desktop sidebar - only shown on large screens */}
        {showSidebar && (
          <aside className="hidden lg:block w-64 border-r border-border sticky top-16 self-start h-[calc(100vh-4rem)]">
            <BlogSidebar />
          </aside>
        )}

        {/* Main content */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* Blog header with mobile sidebar trigger */}
          <div className=" top-16 z-20 flex h-12 items-center gap-4 border-b border-border bg-background/90  px-4 sm:px-6 transition-all duration-300 ease-in-out">
            {showSidebar && <MobileSidebar />}
          </div>

          {/* Main content with proper scrolling */}
          <main 
            ref={mainRef}
            className="flex-1 overflow-y-auto bg-background transition-all duration-300 ease-in-out scroll-smooth"
          >
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="space-y-6"
            >
              {(title || subtitle) && (
                <motion.div
                  variants={itemVariants}
                  className="border-b border-border bg-card/50 py-8 px-4 sm:px-6 lg:px-8 transition-all duration-300 ease-in-out"
                >
                  {title && (
                    <h1 className="text-2xl sm:text-3xl font-bold text-foreground transition-colors duration-300 ease-in-out">{title}</h1>
                  )}
                  {subtitle && (
                    <p className="mt-1 text-muted-foreground transition-colors duration-300 ease-in-out">{subtitle}</p>
                  )}
                </motion.div>
              )}

              <motion.div
                variants={itemVariants}
                className="px-4 sm:px-6 lg:px-8 pb-20 animate-fadeIn"
              >
                {children}
              </motion.div>
            </motion.div>
            
            {/* Scroll to top button */}
            <AnimatePresence>
              {showScrollTop && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.2 }}
                  className="fixed bottom-6 right-6 z-30"
                >
                  <Button
                    size="icon"
                    className="rounded-full shadow-md bg-primary hover:bg-primary/90 text-primary-foreground"
                    onClick={scrollToTop}
                    aria-label="Scroll to top"
                  >
                    <ChevronUp className="h-5 w-5" />
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>
            
            {/* Add animation keyframes */}
            {/* <style dangerouslySetInnerHTML={{ __html: `
              @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
              }
              .animate-fadeIn {
                animation: fadeIn 0.5s ease-out forwards;
              }
            `}} /> */}
            <style jsx global>{`

              @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
              }
              .animate-fadeIn {
                animation: fadeIn 0.5s ease-out forwards;
              }
            `}</style>
          </main>
        </div>
      </div>

      <Footer />
    </div>
  );
}
