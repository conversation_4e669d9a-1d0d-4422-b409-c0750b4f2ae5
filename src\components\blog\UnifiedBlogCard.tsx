'use client';

import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, User, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

interface BlogPost {
  id?: string;
  _id?: string;
  title: string;
  excerpt?: string;
  description?: string;
  content?: string;
  slug: string;
  featuredImage?: string;
  image?: string;
  imageCredit?: string;
  categoryId?: string;
  category?: string;
  tags?: string[];
  publishedAt?: string;
  createdAt?: string;
  date?: string;
  author: {
    name: string;
    email?: string;
  } | string;
}

interface UnifiedBlogCardProps {
  post: BlogPost;
  index?: number;
  showAnimation?: boolean;
  className?: string;
}

export function UnifiedBlogCard({ 
  post, 
  index = 0, 
  showAnimation = true,
  className = ""
}: UnifiedBlogCardProps) {
  
  // Normalize post data
  const normalizedPost = {
    id: post.id || post._id || '',
    title: post.title,
    excerpt: post.excerpt || post.description || '',
    slug: post.slug,
    featuredImage: post.featuredImage || post.image || '',
    imageCredit: post.imageCredit || '',
    category: post.category || 'General',
    publishedAt: post.publishedAt || post.createdAt || post.date || new Date().toISOString(),
    author: typeof post.author === 'string' ? post.author : post.author?.name || 'Unknown Author'
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Recent';
    }
  };

  // Truncate text
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Get category color
  const getCategoryColor = (category: string) => {
    const colors = {
      'Technology': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      'Food': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      'Automotive': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      'Design': 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300',
      'AI': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300',
      'Web': 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-300',
      'General': 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
    };
    return colors[category as keyof typeof colors] || colors.General;
  };

  const cardVariants = showAnimation ? {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: index * 0.1,
        ease: "easeOut"
      }
    }
  } : {};

  const hoverVariants = showAnimation ? {
    y: -4,
    transition: { duration: 0.2 }
  } : {};

  return (
    <motion.div
      variants={cardVariants}
      initial={showAnimation ? "hidden" : false}
      animate={showAnimation ? "visible" : false}
      whileHover={showAnimation ? hoverVariants : undefined}
      className={`group h-full ${className}`}
    >
      <div className="h-full bg-card border border-border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
        {/* Image Section */}
        {normalizedPost.featuredImage && (
          <div className="relative h-48 overflow-hidden">
            <Image
              src={normalizedPost.featuredImage}
              alt={normalizedPost.title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&q=80';
              }}
            />
            
            {/* Category Badge */}
            <div className="absolute top-3 left-3">
              <Badge className={`${getCategoryColor(normalizedPost.category)} text-xs font-medium`}>
                {normalizedPost.category}
              </Badge>
            </div>

            {/* Image Credit */}
            {normalizedPost.imageCredit && (
              <div className="absolute bottom-2 right-2">
                <span className="text-xs text-white/80 bg-black/60 px-2 py-1 rounded backdrop-blur-sm">
                  📸 {normalizedPost.imageCredit}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Content Section */}
        <div className="p-5 flex flex-col h-full">
          {/* Category Badge (if no image) */}
          {!normalizedPost.featuredImage && (
            <div className="mb-3">
              <Badge className={`${getCategoryColor(normalizedPost.category)} text-xs font-medium`}>
                {normalizedPost.category}
              </Badge>
            </div>
          )}

          {/* Title */}
          <h3 className="text-lg font-semibold mb-2 line-clamp-2 text-foreground group-hover:text-primary transition-colors">
            {normalizedPost.title}
          </h3>

          {/* Excerpt */}
          {normalizedPost.excerpt && (
            <p className="text-muted-foreground text-sm mb-4 line-clamp-3 flex-grow">
              {truncateText(normalizedPost.excerpt, 150)}
            </p>
          )}

          {/* Meta Information */}
          <div className="flex items-center gap-4 text-xs text-muted-foreground mb-4">
            <div className="flex items-center gap-1">
              <User className="h-3 w-3" />
              <span>{normalizedPost.author}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(normalizedPost.publishedAt)}</span>
            </div>
          </div>

          {/* Read More Button */}
          <Link href={`/blog/${normalizedPost.slug}`} className="mt-auto">
            <Button 
              variant="ghost" 
              className="w-full justify-between group/btn hover:bg-primary/5 hover:text-primary"
            >
              <span>Read More</span>
              <ArrowRight className="h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
            </Button>
          </Link>
        </div>
      </div>
    </motion.div>
  );
}
